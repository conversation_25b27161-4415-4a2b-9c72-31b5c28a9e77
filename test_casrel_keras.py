#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for CasRel Keras-Core Implementation
"""

import os
import sys
import json
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """Create minimal test data for validation"""
    
    # Create test directory
    os.makedirs('data/test', exist_ok=True)
    
    # Sample data
    sample_data = [
        {
            "text": "<PERSON> was born in Hawaii.",
            "triple_list": [["<PERSON>", "born_in", "Hawaii"]]
        },
        {
            "text": "Apple Inc. is located in Cupertino.",
            "triple_list": [["Apple Inc.", "located_in", "Cupertino"]]
        }
    ]
    
    # Create relation mapping
    rel2id = {
        "born_in": 0,
        "located_in": 1
    }
    id2rel = {str(v): k for k, v in rel2id.items()}
    
    # Save test data
    with open('data/test/train_triples.json', 'w') as f:
        json.dump(sample_data, f, indent=2)
    
    with open('data/test/dev_triples.json', 'w') as f:
        json.dump(sample_data[:1], f, indent=2)
    
    with open('data/test/test_triples.json', 'w') as f:
        json.dump(sample_data[:1], f, indent=2)
    
    with open('data/test/rel2id.json', 'w') as f:
        json.dump([id2rel, rel2id], f, indent=2)
    
    print("Test data created successfully!")
    return sample_data, rel2id, id2rel


def test_model_initialization():
    """Test model initialization"""
    try:
        from casrel_keras import CasRelModel, HBTokenizer
        from transformers import AutoTokenizer
        
        print("Testing model initialization...")
        
        # Initialize model
        model = CasRelModel('bert-base-cased', num_rels=2, max_length=64)
        
        print("✓ Model initialized successfully")
        print(f"✓ Subject model: {model.subject_model}")
        print(f"✓ Object model: {model.object_model}")
        print(f"✓ Training model: {model.train_model}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model initialization failed: {e}")
        return False


def test_tokenizer():
    """Test custom tokenizer"""
    try:
        from casrel_keras import HBTokenizer
        from transformers import AutoTokenizer
        
        print("\nTesting tokenizer...")
        
        bert_tokenizer = AutoTokenizer.from_pretrained('bert-base-cased')
        tokenizer = HBTokenizer(bert_tokenizer)
        
        test_text = "Barack Obama was born in Hawaii."
        tokens = tokenizer.tokenize(test_text)
        token_ids, segment_ids = tokenizer.encode(test_text)
        
        print(f"✓ Text: {test_text}")
        print(f"✓ Tokens: {tokens[:10]}...")  # Show first 10 tokens
        print(f"✓ Token IDs length: {len(token_ids)}")
        print(f"✓ Segment IDs length: {len(segment_ids)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Tokenizer test failed: {e}")
        return False


def test_data_loading():
    """Test data loading functionality"""
    try:
        from casrel_keras import load_data, DataGenerator
        
        print("\nTesting data loading...")
        
        # Create test data first
        create_test_data()
        
        # Load data
        train_data, dev_data, test_data, id2rel, rel2id = load_data(
            'data/test/train_triples.json',
            'data/test/dev_triples.json', 
            'data/test/test_triples.json',
            'data/test/rel2id.json'
        )
        
        print(f"✓ Train data: {len(train_data)} samples")
        print(f"✓ Dev data: {len(dev_data)} samples")
        print(f"✓ Test data: {len(test_data)} samples")
        print(f"✓ Relations: {len(rel2id)}")
        print(f"✓ Sample triple: {train_data[0]['triple_list'][0]}")
        
        return True
        
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        return False


def test_evaluation_functions():
    """Test evaluation functions"""
    try:
        from casrel_keras import partial_match
        
        print("\nTesting evaluation functions...")
        
        # Test partial match
        pred_set = {("Barack Obama", "born_in", "Hawaii")}
        gold_set = {("Barack", "born_in", "Hawaii")}
        
        pred_partial, gold_partial = partial_match(pred_set, gold_set)
        
        print(f"✓ Original pred: {pred_set}")
        print(f"✓ Original gold: {gold_set}")
        print(f"✓ Partial pred: {pred_partial}")
        print(f"✓ Partial gold: {gold_partial}")
        print(f"✓ Match: {pred_partial == gold_partial}")
        
        return True
        
    except Exception as e:
        print(f"✗ Evaluation test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("=" * 50)
    print("CasRel Keras-Core Implementation Tests")
    print("=" * 50)
    
    tests = [
        test_data_loading,
        test_tokenizer,
        test_evaluation_functions,
        test_model_initialization,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! The implementation looks good.")
        print("\nNext steps:")
        print("1. Install required dependencies:")
        print("   pip install keras-core torch transformers tqdm")
        print("2. Prepare your dataset in the required format")
        print("3. Run training with: python casrel_keras.py --train --dataset your_dataset")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    print("=" * 50)


if __name__ == '__main__':
    main()
