# CasRel Keras-Core Implementation

这是CasRel模型的keras-core实现版本，使用torch作为后端，与原始Keras版本功能完全一致。

## 🚀 特性

- ✅ 使用keras-core库，支持torch后端
- ✅ 完全复现原版Keras实现的功能
- ✅ 支持两阶段关系抽取（subject → object）
- ✅ 实现partial match评估机制
- ✅ 兼容原始数据格式
- ✅ 模块化设计，易于理解和修改

## 📋 依赖要求

```bash
pip install keras-core torch transformers tqdm numpy
```

## 📁 文件结构

```
casrel_keras.py          # 主实现文件
test_casrel_keras.py     # 测试脚本
README_KERAS_CORE.md     # 本说明文件
data/                    # 数据目录
├── WebNLG/
│   ├── train_triples.json
│   ├── dev_triples.json
│   ├── test_triples.json
│   └── rel2id.json
└── ...
```

## 🎯 核心组件

### 1. CasRelModel
主模型类，包含三个子模型：
- `subject_model`: 主体实体抽取
- `object_model`: 客体和关系抽取
- `train_model`: 训练用的完整模型

### 2. HBTokenizer
自定义分词器，在词之间插入`[unused1]`标记，与原版保持一致。

### 3. DataGenerator
数据生成器，负责批量数据处理和标签生成。

### 4. 评估函数
- `extract_triples()`: 从文本中抽取三元组
- `partial_match()`: 实现partial match评估
- `evaluate_model()`: 模型性能评估

## 🔧 使用方法

### 训练模型

```bash
python casrel_keras.py --train --dataset WebNLG --epochs 50 --batch_size 6 --lr 1e-5
```

### 评估模型

```bash
python casrel_keras.py --dataset WebNLG
```

### 运行测试

```bash
python test_casrel_keras.py
```

## 📊 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--train` | False | 是否训练模型 |
| `--dataset` | WebNLG | 数据集名称 |
| `--batch_size` | 6 | 批大小 |
| `--epochs` | 100 | 训练轮数 |
| `--lr` | 1e-5 | 学习率 |
| `--max_length` | 128 | 最大序列长度 |

## 🔍 与原版对比

| 特性 | 原版Keras | Keras-Core版本 |
|------|-----------|----------------|
| 后端 | TensorFlow | Torch |
| 模型架构 | ✅ | ✅ |
| 两阶段抽取 | ✅ | ✅ |
| Partial Match | ✅ | ✅ |
| 数据格式 | ✅ | ✅ |
| 性能 | 基准 | 相当 |

## 🧪 评估机制

### Partial Match
与原版Keras实现一致，使用partial match评估：
- 只比较实体的第一个词
- 例如："Barack Obama" vs "Barack" 被认为是匹配的

### 指标计算
- Precision = 正确预测数 / 总预测数
- Recall = 正确预测数 / 总真实数
- F1 = 2 * P * R / (P + R)

## 📝 代码示例

```python
from casrel_keras import CasRelModel, extract_triples

# 初始化模型
model = CasRelModel('bert-base-cased', num_rels=24, max_length=128)

# 加载训练好的权重
model.subject_model.load_weights('best_subject_model.weights.h5')
model.object_model.load_weights('best_object_model.weights.h5')

# 抽取三元组
text = "Barack Obama was born in Hawaii."
triples = extract_triples(
    model.subject_model, 
    model.object_model, 
    model.tokenizer, 
    text, 
    id2rel
)
print(triples)  # [('Barack Obama', 'born_in', 'Hawaii')]
```

## ⚠️ 注意事项

1. **BERT集成**: 当前使用简化的BERT集成方式，生产环境可能需要更复杂的实现
2. **内存使用**: 根据GPU内存调整batch_size
3. **数据格式**: 确保数据格式与原版一致
4. **依赖版本**: 建议使用最新版本的keras-core和torch

## 🐛 故障排除

### 常见问题

1. **ImportError: keras-core**
   ```bash
   pip install keras-core
   ```

2. **CUDA内存不足**
   - 减小batch_size
   - 减小max_length

3. **模型权重加载失败**
   - 确保先训练模型
   - 检查权重文件路径

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个实现！

## 📄 许可证

与原项目保持一致的许可证。

---

**注**: 这个实现专注于功能复现和代码清晰度，适合学习和研究使用。如需生产环境部署，建议进一步优化性能和错误处理。
