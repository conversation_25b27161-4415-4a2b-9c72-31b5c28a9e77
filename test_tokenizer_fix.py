#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the tokenizer fix
"""

import sys
import unicodedata

try:
    from transformers import AutoTokenizer
    print("✓ transformers imported successfully")
except ImportError:
    print("✗ transformers not found. Please install with: pip install transformers")
    sys.exit(1)


class SimpleHBTokenizer:
    """Simplified HBTokenizer that works with transformers tokenizer"""
    def __init__(self, bert_tokenizer, cased=True):
        self.bert_tokenizer = bert_tokenizer
        self._cased = cased
        
    def tokenize(self, text):
        """Tokenize text following the original HBTokenizer logic"""
        if not self._cased:
            text = unicodedata.normalize('NFD', text)
            text = ''.join([ch for ch in text if unicodedata.category(ch) != 'Mn'])
            text = text.lower()
        
        spaced = ''
        for ch in text:
            if ord(ch) == 0 or ord(ch) == 0xfffd or self._is_control(ch):
                continue
            else:
                spaced += ch
                
        tokens = []
        for word in spaced.strip().split():
            # Use the underlying tokenizer's wordpiece tokenization
            word_tokens = self.bert_tokenizer.tokenize(word)
            tokens.extend(word_tokens)
            tokens.append('[unused1]')
        return tokens
    
    def _is_control(self, ch):
        """Check if character is control character"""
        if ch == '\t' or ch == '\n' or ch == '\r':
            return False
        cat = unicodedata.category(ch)
        if cat.startswith('C'):
            return True
        return False
    
    def encode(self, first, second=None):
        """Encode text to token ids"""
        tokens = self.tokenize(first)
        if len(tokens) > 510:  # Leave space for [CLS] and [SEP]
            tokens = tokens[:510]
        
        # Add special tokens
        tokens = ['[CLS]'] + tokens + ['[SEP]']
        
        # Convert to ids using the bert tokenizer
        token_ids = self.bert_tokenizer.convert_tokens_to_ids(tokens)
        segment_ids = [0] * len(token_ids)
        
        return token_ids, segment_ids


def test_tokenizer():
    """Test the SimpleHBTokenizer"""
    print("Testing SimpleHBTokenizer...")
    
    try:
        # Initialize BERT tokenizer
        bert_tokenizer = AutoTokenizer.from_pretrained('bert-base-cased')
        print("✓ BERT tokenizer loaded")
        
        # Initialize HB tokenizer
        hb_tokenizer = SimpleHBTokenizer(bert_tokenizer, cased=True)
        print("✓ HBTokenizer initialized")
        
        # Test tokenization
        test_text = "Barack Obama was born in Hawaii."
        tokens = hb_tokenizer.tokenize(test_text)
        print(f"✓ Tokenized: '{test_text}'")
        print(f"  Tokens: {tokens[:10]}...")  # Show first 10 tokens
        
        # Test encoding
        token_ids, segment_ids = hb_tokenizer.encode(test_text)
        print(f"✓ Encoded to {len(token_ids)} token IDs")
        print(f"  First 10 IDs: {token_ids[:10]}")
        
        # Test with entity
        entity_text = "Barack Obama"
        entity_tokens = hb_tokenizer.tokenize(entity_text)
        print(f"✓ Entity tokenized: '{entity_text}' -> {entity_tokens}")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False


def test_find_head_idx():
    """Test the find_head_idx function"""
    print("\nTesting find_head_idx function...")
    
    def find_head_idx(source, target):
        """Find the starting index of target in source"""
        target_len = len(target)
        for i in range(len(source)):
            if source[i: i + target_len] == target:
                return i
        return -1
    
    # Test cases
    test_cases = [
        (["Barack", "[unused1]", "Obama", "[unused1]", "was", "born"], ["Barack", "[unused1]", "Obama"], 0),
        (["The", "[unused1]", "Barack", "[unused1]", "Obama", "[unused1]", "was"], ["Barack", "[unused1]", "Obama"], 2),
        (["Hello", "world"], ["world"], 1),
        (["Hello", "world"], ["not", "found"], -1),
    ]
    
    for i, (source, target, expected) in enumerate(test_cases):
        result = find_head_idx(source, target)
        if result == expected:
            print(f"✓ Test case {i+1}: {result} == {expected}")
        else:
            print(f"✗ Test case {i+1}: {result} != {expected}")
            return False
    
    return True


def main():
    """Run all tests"""
    print("=" * 50)
    print("Tokenizer Fix Verification")
    print("=" * 50)
    
    tests = [
        test_tokenizer,
        test_find_head_idx,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! The tokenizer fix works correctly.")
        print("\nThe SimpleHBTokenizer should now work with transformers tokenizer.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    print("=" * 50)


if __name__ == '__main__':
    main()
