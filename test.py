#! -*- coding:utf-8 -*-
"""
Script to count negative samples in CasRelDataset.

Negative samples are defined as sentences where, after strict entity matching
and token mapping, no valid triples can be identified (s2ro_map is empty).
These samples will result in all-zero labels for subject and object predictions.
"""
import torch
import json
import re # Make sure 're' is imported for regex operations
import random # Make sure 'random' is imported for random.choice (though not strictly needed for negative sample detection, it's part of __getitem__)
from transformers import AutoTokenizer
from tqdm import tqdm
import argparse
import os

# --- Copy/Paste your CasRelDataset class here ---
# Ensure it's identical to the one you are using for training.
class CasRelDataset(torch.utils.data.Dataset):
    """
    The final, robust version of the CasRel Dataset.
    - Uses word boundary (`\b`) regex matching to avoid substring errors.
    - Filters out noisy/badly-aligned triples automatically.
    """
    def __init__(self, data, tokenizer, rel2id, max_len=128):
        self.data = data
        self.tokenizer = tokenizer
        self.rel2id = rel2id
        self.max_len = max_len

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        text = item['text']

        encoding = self.tokenizer(
            text,
            max_length=self.max_len,
            truncation=True,
            padding='max_length',
            return_offsets_mapping=True
        )
        input_ids = torch.tensor(encoding['input_ids'])
        attention_mask = torch.tensor(encoding['attention_mask'])
        offset_mapping = encoding['offset_mapping']

        seq_len = len(input_ids)
        sub_heads = torch.zeros(seq_len)
        sub_tails = torch.zeros(seq_len)
        obj_heads = torch.zeros(seq_len, len(self.rel2id))
        obj_tails = torch.zeros(seq_len, len(self.rel2id))

        s2ro_map = {}
        for triple in item['triple_list']:
            subj_text, rel, obj_text = triple

            subj_pattern = r'\b' + re.escape(subj_text) + r'\b'
            obj_pattern = r'\b' + re.escape(obj_text) + r'\b'
            subj_match = re.search(subj_pattern, text)
            obj_match = re.search(obj_pattern, text)

            if not subj_match or not obj_match:
                continue

            subj_char_start, subj_char_end = subj_match.span()
            obj_char_start, obj_char_end = obj_match.span()

            sub_token_start, sub_token_end = -1, -1
            obj_token_start, obj_token_end = -1, -1

            for i, (start, end) in enumerate(offset_mapping):
                if start == end: continue
                if start <= subj_char_start < end: sub_token_start = i
                if start < subj_char_end <= end: sub_token_end = i
                if start <= obj_char_start < end: obj_token_start = i
                if start < obj_char_end <= end: obj_token_end = i

            if sub_token_start != -1 and sub_token_end != -1 and obj_token_start != -1 and obj_token_end != -1:
                sub_pos = (sub_token_start, sub_token_end)
                if sub_pos not in s2ro_map:
                    s2ro_map[sub_pos] = []
                rel_id = self.rel2id[rel]
                s2ro_map[sub_pos].append((obj_token_start, obj_token_end, rel_id))

        # This part determines if it's a negative sample
        is_negative_sample = not bool(s2ro_map) # True if s2ro_map is empty

        if s2ro_map:
            sub_heads[list(s[0] for s in s2ro_map.keys())] = 1
            sub_tails[list(s[1] for s in s2ro_map.keys())] = 1
            
            sub_pos = random.choice(list(s2ro_map.keys()))
            sub_head_idx = torch.tensor([sub_pos[0]])
            sub_tail_idx = torch.tensor([sub_pos[1]])

            for obj_start, obj_end, rel_id in s2ro_map.get(sub_pos, []):
                obj_heads[obj_start, rel_id] = 1
                obj_tails[obj_end, rel_id] = 1
        else:
            sub_head_idx = torch.tensor([0])
            sub_tail_idx = torch.tensor([0])

        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'sub_heads': sub_heads,
            'sub_tails': sub_tails,
            'obj_heads': obj_heads,
            'obj_tails': obj_tails,
            'sub_head_idx': sub_head_idx,
            'sub_tail_idx': sub_tail_idx,
            'is_negative_sample': is_negative_sample # Add this flag for counting
        }

# --- Copy/Paste your load_data function here ---
def load_data(train_path, dev_path, test_path, rel_path):
    """Load and return data"""
    def load_json(path):
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    with open(rel_path, 'r', encoding='utf-8') as f:
        rel_data = json.load(f)

    id2rel = {int(k): v for k, v in rel_data[0].items()}
    rel2id = rel_data[1]
    
    return (load_json(train_path), load_json(dev_path), 
            load_json(test_path), id2rel, rel2id)

# --- Main counting logic ---
def count_negative_samples(dataset_instance, split_name):
    total_samples = len(dataset_instance)
    negative_samples_count = 0
    positive_samples_count = 0

    print(f"\nCounting negative samples for {split_name} split ({total_samples} total samples):")
    for i in tqdm(range(total_samples), desc=f"Processing {split_name}"):
        item = dataset_instance[i]
        if item['is_negative_sample']:
            negative_samples_count += 1
        else:
            positive_samples_count += 1
    
    neg_percentage = (negative_samples_count / total_samples) * 100 if total_samples > 0 else 0
    
    print(f"--- {split_name} Split Statistics ---")
    print(f"Total Samples: {total_samples}")
    print(f"Positive Samples (at least one valid triple): {positive_samples_count}")
    print(f"Negative Samples (no valid triples): {negative_samples_count}")
    print(f"Percentage of Negative Samples: {neg_percentage:.2f}%")
    print("-------------------------------------")
    return negative_samples_count, total_samples

def main():
    parser = argparse.ArgumentParser(description='Count negative samples in CasRelDataset.')
    parser.add_argument('--dataset', default='NYT', help='Dataset name (e.g., NYT)')
    args = parser.parse_args()

    # Setup
    dataset_name = args.dataset
    data_paths = {
        "train": f'data/{dataset_name}/train_triples.json',
        "dev": f'data/{dataset_name}/dev_triples.json',
        "test": f'data/{dataset_name}/test_triples.json',
        "rel": f'data/{dataset_name}/rel2id.json'
    }

    for path in data_paths.values():
        if not os.path.exists(path):
            print(f"Error: Data file not found at {path}")
            print("Please ensure your data is in the 'data/{dataset_name}/' directory or change the path.")
            return

    # Load data
    train_data, dev_data, test_data, id2rel, rel2id = load_data(
        data_paths['train'], data_paths['dev'], data_paths['test'], data_paths['rel']
    )

    # Initialize tokenizer (max_len should be consistent with training)
    tokenizer = AutoTokenizer.from_pretrained('bert-base-cased', use_fast=True)
    MAX_LEN = 128 # Assume this is your max_len in training

    # Create Dataset instances
    train_dataset = CasRelDataset(train_data, tokenizer, rel2id, MAX_LEN)
    dev_dataset = CasRelDataset(dev_data, tokenizer, rel2id, MAX_LEN)
    test_dataset = CasRelDataset(test_data, tokenizer, rel2id, MAX_LEN)

    # Count for each split
    count_negative_samples(train_dataset, "Train")
    count_negative_samples(dev_dataset, "Dev")
    count_negative_samples(test_dataset, "Test")

if __name__ == '__main__':
    main()