#! -*- coding:utf-8 -*-
"""
CasRel PyTorch Implementation - Simplified Version
A clean, easy-to-understand implementation of CasRel model
"""
import torch
import torch.nn as nn
import json
import numpy as np
from transformers import BertModel, AutoTokenizer
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import argparse
import os
import random
import re
class CasRelModel(nn.Module):
    """Simplified CasRel Model"""
    def __init__(self, bert_model, num_rels):
        super().__init__()
        self.bert = BertModel.from_pretrained(bert_model)
        hidden_size = self.bert.config.hidden_size
        
        # Subject extraction layers
        self.sub_heads = nn.Linear(hidden_size, 1)
        self.sub_tails = nn.Linear(hidden_size, 1)
        
        # Object extraction layers  
        self.obj_heads = nn.Linear(hidden_size, num_rels)
        self.obj_tails = nn.Linear(hidden_size, num_rels)
        
    def forward(self, input_ids, attention_mask, sub_head_idx=None, sub_tail_idx=None):
        # BERT encoding
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        sequence_output = outputs.last_hidden_state  # [batch, seq_len, hidden]
        
        # Subject extraction
        sub_heads_logits = self.sub_heads(sequence_output).squeeze(-1)  # [batch, seq_len]
        sub_tails_logits = self.sub_tails(sequence_output).squeeze(-1)  # [batch, seq_len]
        sub_heads_pred = torch.sigmoid(sub_heads_logits)
        sub_tails_pred = torch.sigmoid(sub_tails_logits)
        
        # Object extraction (only if subject positions provided)
        if sub_head_idx is not None and sub_tail_idx is not None:
            # Get subject features
            batch_size = sequence_output.size(0)
            sub_head_features = sequence_output[torch.arange(batch_size), sub_head_idx.squeeze()]
            sub_tail_features = sequence_output[torch.arange(batch_size), sub_tail_idx.squeeze()]
            sub_features = (sub_head_features + sub_tail_features) / 2  # [batch, hidden]
            
            # Add subject info to all positions
            sub_features = sub_features.unsqueeze(1).expand(-1, sequence_output.size(1), -1)
            enhanced_output = sequence_output + sub_features
            
            # Object prediction
            obj_heads_logits = self.obj_heads(enhanced_output)  # [batch, seq_len, num_rels]
            obj_tails_logits = self.obj_tails(enhanced_output)  # [batch, seq_len, num_rels]
            obj_heads_pred = torch.sigmoid(obj_heads_logits)
            obj_tails_pred = torch.sigmoid(obj_tails_logits)
            
            return sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred
        
        return sub_heads_pred, sub_tails_pred

class CasRelDataset(Dataset):
    """
    The final, robust version of the CasRel Dataset.
    - Uses word boundary (`\b`) regex matching to avoid substring errors.
    - Filters out noisy/badly-aligned triples automatically.
    """
    def __init__(self, data, tokenizer, rel2id, max_len=128):
        self.data = data
        self.tokenizer = tokenizer
        self.rel2id = rel2id
        self.max_len = max_len

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        text = item['text']

        encoding = self.tokenizer(
            text,
            max_length=self.max_len,
            truncation=True,
            padding='max_length',
            return_offsets_mapping=True
        )
        input_ids = torch.tensor(encoding['input_ids'])
        attention_mask = torch.tensor(encoding['attention_mask'])
        offset_mapping = encoding['offset_mapping']

        seq_len = len(input_ids)
        sub_heads = torch.zeros(seq_len)
        sub_tails = torch.zeros(seq_len)
        obj_heads = torch.zeros(seq_len, len(self.rel2id))
        obj_tails = torch.zeros(seq_len, len(self.rel2id))

        s2ro_map = {}
        for triple in item['triple_list']:
            subj_text, rel, obj_text = triple

            # --- SOLUTION 2: Strict Word Boundary Matching ---
            subj_pattern = r'\b' + re.escape(subj_text) + r'\b'
            obj_pattern = r'\b' + re.escape(obj_text) + r'\b'
            subj_match = re.search(subj_pattern, text)
            obj_match = re.search(obj_pattern, text)

            if not subj_match or not obj_match:
                continue # Skip triples that are not whole-word matches

            subj_char_start, subj_char_end = subj_match.span()
            obj_char_start, obj_char_end = obj_match.span()
            # --- End of Change ---

            # Map character spans to token spans
            sub_token_start, sub_token_end = -1, -1
            obj_token_start, obj_token_end = -1, -1

            for i, (start, end) in enumerate(offset_mapping):
                if start == end: continue
                if start <= subj_char_start < end: sub_token_start = i
                if start < subj_char_end <= end: sub_token_end = i
                if start <= obj_char_start < end: obj_token_start = i
                if start < obj_char_end <= end: obj_token_end = i

            if sub_token_start != -1 and sub_token_end != -1 and obj_token_start != -1 and obj_token_end != -1:
                sub_pos = (sub_token_start, sub_token_end)
                if sub_pos not in s2ro_map:
                    s2ro_map[sub_pos] = []
                rel_id = self.rel2id[rel]
                s2ro_map[sub_pos].append((obj_token_start, obj_token_end, rel_id))

        if s2ro_map:
            sub_heads[list(s[0] for s in s2ro_map.keys())] = 1
            sub_tails[list(s[1] for s in s2ro_map.keys())] = 1
            
            # Randomly select one subject to train its corresponding objects
            sub_pos = random.choice(list(s2ro_map.keys()))
            sub_head_idx = torch.tensor([sub_pos[0]])
            sub_tail_idx = torch.tensor([sub_pos[1]])

            for obj_start, obj_end, rel_id in s2ro_map.get(sub_pos, []):
                obj_heads[obj_start, rel_id] = 1
                obj_tails[obj_end, rel_id] = 1
        else:
            # If no valid triples, use default values
            sub_head_idx = torch.tensor([0])
            sub_tail_idx = torch.tensor([0])

        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'sub_heads': sub_heads,
            'sub_tails': sub_tails,
            'obj_heads': obj_heads,
            'obj_tails': obj_tails,
            'sub_head_idx': sub_head_idx,
            'sub_tail_idx': sub_tail_idx
        }




def load_data(train_path, dev_path, test_path, rel_path):
    """Load and return data"""
    def load_json(path):
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    with open(rel_path, 'r', encoding='utf-8') as f:
        rel_data = json.load(f)

    # rel_data is a list: [id2rel_dict, rel2id_dict]
    id2rel = {int(k): v for k, v in rel_data[0].items()}
    rel2id = rel_data[1]
    
    return (load_json(train_path), load_json(dev_path), 
            load_json(test_path), id2rel, rel2id)

def extract_triples(model, tokenizer, text, id2rel, device, threshold=0.5):
    """Extract triples from text"""
    model.eval()
    
    # Tokenize
    encoding = tokenizer(text, return_tensors='pt', max_length=128, truncation=True)
    input_ids = encoding['input_ids'].to(device)
    attention_mask = encoding['attention_mask'].to(device)
    tokens = tokenizer.convert_ids_to_tokens(input_ids[0])
    
    with torch.no_grad():
        # Subject extraction
        sub_heads_pred, sub_tails_pred = model(input_ids, attention_mask)
        
        # Find subjects
        sub_heads = torch.where(sub_heads_pred[0] > threshold)[0].cpu().numpy()
        sub_tails = torch.where(sub_tails_pred[0] > threshold)[0].cpu().numpy()
        
        subjects = []
        for h in sub_heads:
            for t in sub_tails:
                if h <= t:
                    subjects.append((h, t))
                    break
        
        # Extract objects for each subject
        triples = []
        for sub_head, sub_tail in subjects:
            sub_head_idx = torch.tensor([[sub_head]], device=device)
            sub_tail_idx = torch.tensor([[sub_tail]], device=device)
            
            _, _, obj_heads_pred, obj_tails_pred = model(
                input_ids, attention_mask, sub_head_idx, sub_tail_idx)
            
            # Find objects
            obj_heads = torch.where(obj_heads_pred[0] > threshold)
            obj_tails = torch.where(obj_tails_pred[0] > threshold)
            
            for oh, or_h in zip(obj_heads[0].cpu(), obj_heads[1].cpu()):
                for ot, or_t in zip(obj_tails[0].cpu(), obj_tails[1].cpu()):
                    if oh <= ot and or_h == or_t:
                        # Extract text (use same range as original: exclusive tail)
                        sub_tokens = tokens[sub_head:sub_tail+1]  # +1 because our tail is inclusive
                        obj_tokens = tokens[oh:ot+1]  # +1 because our tail is inclusive
                        rel = id2rel[or_h.item()]

                        # Clean text for BertTokenizer (different from HBTokenizer)
                        sub_text = tokenizer.convert_tokens_to_string(sub_tokens)
                        obj_text = tokenizer.convert_tokens_to_string(obj_tokens)
                        
                        triples.append((sub_text, rel, obj_text))
                        break
    
    return list(set(triples))  # Remove duplicates

def partial_match_torch(triples_set):
    # 将一个set中的三元组转为部分匹配模式
    new_set = set()
    for s, r, o in triples_set:
        # 只取实体字符串的第一个词
        s_partial = s.strip().split(' ')[0]
        o_partial = o.strip().split(' ')[0]
        new_set.add((s_partial, r, o_partial))
    return new_set

def calculate_metrics(pred_triples, gold_triples, use_partial=True): # 增加开关
    """Calculate precision, recall, F1, with optional partial matching."""
    if use_partial:
        # 使用部分匹配来评估
        pred_set = partial_match_torch(pred_triples)
        gold_set = partial_match_torch([tuple(g) for g in gold_triples]) # 保证gold也是tuple set
    else:
        # 严格匹配
        pred_set = set(pred_triples)
        gold_set = set([tuple(g) for g in gold_triples])

    # ...后续计算不变...
    if len(pred_set) == 0: precision = 0
    else: precision = len(pred_set & gold_set) / len(pred_set)
    if len(gold_set) == 0: recall = 0
    else: recall = len(pred_set & gold_set) / len(gold_set)
    if precision + recall == 0: f1 = 0
    else: f1 = 2 * precision * recall / (precision + recall)
    return precision, recall, f1

def evaluate_model(model, test_data, tokenizer, id2rel, device):
    """Evaluation function"""
    model.eval()
    all_pred_triples = []
    all_gold_triples = []

    for item in tqdm(test_data, desc='Evaluating'):
        text = item['text']
        # 注意：gold_triples现在应该是list of lists
        gold_triples = [list(triple) for triple in item['triple_list']] 
        pred_triples = extract_triples(model, tokenizer, text, id2rel, device)

        all_pred_triples.extend(pred_triples)
        all_gold_triples.extend(gold_triples)

    # 在这里调用，并传入use_partial=True
    return calculate_metrics(all_pred_triples, all_gold_triples, use_partial=True)

def train_model(model, train_loader, dev_data, tokenizer, id2rel, device, epochs=10):
    """Training function"""
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-5)
    criterion = nn.BCELoss(reduction='none')
    
    best_f1 = 0
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs}')

        for batch in progress_bar:
            optimizer.zero_grad()

            # Move to device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            sub_heads_gold = batch['sub_heads'].to(device)
            sub_tails_gold = batch['sub_tails'].to(device)
            obj_heads_gold = batch['obj_heads'].to(device)
            obj_tails_gold = batch['obj_tails'].to(device)
            sub_head_idx = batch['sub_head_idx'].to(device)
            sub_tail_idx = batch['sub_tail_idx'].to(device)

            # Forward pass
            sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred = model(
                input_ids, attention_mask, sub_head_idx, sub_tail_idx)

            # Calculate loss
            mask = attention_mask.float()

            sub_heads_loss = criterion(sub_heads_pred, sub_heads_gold)
            sub_heads_loss = (sub_heads_loss * mask).sum() / mask.sum()

            sub_tails_loss = criterion(sub_tails_pred, sub_tails_gold)
            sub_tails_loss = (sub_tails_loss * mask).sum() / mask.sum()

            obj_heads_loss = criterion(obj_heads_pred, obj_heads_gold)
            # obj_heads_loss shape: [batch_size, seq_len, num_rels]
            # Sum over relations dimension, then apply mask
            obj_heads_loss = (obj_heads_loss.sum(dim=-1) * mask).sum() / mask.sum()

            obj_tails_loss = criterion(obj_tails_pred, obj_tails_gold)
            # obj_tails_loss shape: [batch_size, seq_len, num_rels]
            # Sum over relations dimension, then apply mask
            obj_tails_loss = (obj_tails_loss.sum(dim=-1) * mask).sum() / mask.sum()

            loss = sub_heads_loss + sub_tails_loss + obj_heads_loss + obj_tails_loss

            # Backward pass
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})

        # Evaluation
        if (epoch + 1) % 2 == 0:  # Evaluate every 2 epochs
            precision, recall, f1 = evaluate_model(model, dev_data, tokenizer, id2rel, device)
            print(f'Epoch {epoch+1}: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}')
            if f1 > best_f1:
                best_f1 = f1
                torch.save(model.state_dict(), 'best_model.pt')
                print(f'New best F1: {f1:.3f}')
            model.train()


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='CasRel PyTorch - Simplified')
    parser.add_argument('--dataset', default='NYT', help='Dataset name')
    parser.add_argument('--batch_size', default=16, type=int, help='Batch size')
    parser.add_argument('--epochs', default=10, type=int, help='Training epochs')
    parser.add_argument('--train', action='store_true', help='Train model')
    args = parser.parse_args()

    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    # Load data
    dataset = args.dataset
    train_data, dev_data, test_data, id2rel, rel2id = load_data(
        f'data/{dataset}/train_triples.json',
        f'data/{dataset}/dev_triples.json',
        f'data/{dataset}/test_triples.json',
        f'data/{dataset}/rel2id.json'
    )

    print(f'Loaded {len(train_data)} train, {len(dev_data)} dev, {len(test_data)} test samples')
    print(f'Number of relations: {len(rel2id)}')

    # Initialize model
    tokenizer = AutoTokenizer.from_pretrained('bert-base-cased', use_fast=True)
    model = CasRelModel('bert-base-cased', len(rel2id)).to(device)

    if args.train:
        # Training
        train_dataset = CasRelDataset(train_data, tokenizer, rel2id)
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)

        print('Starting training...')
        train_model(model, train_loader, dev_data, tokenizer, id2rel, device, args.epochs)
    else:
        # Load trained model
        if os.path.exists('best_model.pt'):
            model.load_state_dict(torch.load('best_model.pt', map_location=device))
            print('Loaded trained model')
        else:
            print('No trained model found. Please train first with --train')
            return

    # Final evaluation
    precision, recall, f1 = evaluate_model(model, test_data, tokenizer, id2rel, device)
    print(f'Final Results: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}')

if __name__ == '__main__':
    main()
