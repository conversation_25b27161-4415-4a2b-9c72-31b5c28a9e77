#! -*- coding:utf-8 -*-
"""
CasRel Keras-Core Implementation with Torch Backend
A keras-core implementation of CasRel model that can run with torch backend
"""

import os
os.environ["KERAS_BACKEND"] = "torch"

try:
    import keras_core as keras
except ImportError:
    print("keras-core not found. Please install with: pip install keras-core")
    exit(1)

try:
    import torch
except ImportError:
    print("torch not found. Please install with: pip install torch")
    exit(1)

import numpy as np
import json
import argparse
import unicodedata
import codecs
from tqdm import tqdm

try:
    from transformers import BertModel, AutoTokenizer
except ImportError:
    print("transformers not found. Please install with: pip install transformers")
    exit(1)

import re
import warnings
warnings.filterwarnings('ignore')


class HBTokenizer:
    """Custom tokenizer that inserts [unused1] between words"""
    def __init__(self, tokenizer):
        self.tokenizer = tokenizer
        
    def tokenize(self, text):
        # Normalize text
        text = unicodedata.normalize('NFD', text)
        text = ''.join([ch for ch in text if unicodedata.category(ch) != 'Mn'])
        text = text.lower()
        
        # Clean text
        spaced = ''
        for ch in text:
            if ord(ch) == 0 or ord(ch) == 0xfffd or self._is_control(ch):
                continue
            else:
                spaced += ch
                
        # Tokenize words and add [unused1] between them
        tokens = []
        for word in spaced.strip().split():
            word_tokens = self.tokenizer.tokenize(word)
            tokens.extend(word_tokens)
            tokens.append('[unused1]')
        return tokens
    
    def _is_control(self, ch):
        """Check if character is control character"""
        if ch == '\t' or ch == '\n' or ch == '\r':
            return False
        cat = unicodedata.category(ch)
        if cat.startswith('C'):
            return True
        return False
    
    def encode(self, text, max_length=512):
        """Encode text to token ids"""
        tokens = self.tokenize(text)
        if len(tokens) > max_length - 2:
            tokens = tokens[:max_length - 2]
        
        # Add special tokens
        tokens = ['[CLS]'] + tokens + ['[SEP]']
        
        # Convert to ids
        token_ids = self.tokenizer.convert_tokens_to_ids(tokens)
        segment_ids = [0] * len(token_ids)
        
        return token_ids, segment_ids


class CasRelModel:
    """CasRel Model using keras-core with torch backend"""
    
    def __init__(self, bert_model_name, num_rels, max_length=128):
        self.bert_model_name = bert_model_name
        self.num_rels = num_rels
        self.max_length = max_length
        
        # Initialize BERT tokenizer
        self.bert_tokenizer = AutoTokenizer.from_pretrained(bert_model_name)
        self.tokenizer = HBTokenizer(self.bert_tokenizer)
        
        # Build models
        self.subject_model = self._build_subject_model()
        self.object_model = self._build_object_model()
        self.train_model = self._build_train_model()
        
    def _build_subject_model(self):
        """Build subject extraction model"""
        # Input layers
        token_ids = keras.layers.Input(shape=(None,), dtype='int32', name='token_ids')
        segment_ids = keras.layers.Input(shape=(None,), dtype='int32', name='segment_ids')
        
        # BERT encoding
        bert_output = self._get_bert_layer()([token_ids, segment_ids])
        
        # Subject head and tail prediction
        sub_heads = keras.layers.Dense(1, activation='sigmoid', name='sub_heads')(bert_output)
        sub_tails = keras.layers.Dense(1, activation='sigmoid', name='sub_tails')(bert_output)
        
        model = keras.Model([token_ids, segment_ids], [sub_heads, sub_tails])
        return model
    
    def _build_object_model(self):
        """Build object extraction model"""
        # Input layers
        token_ids = keras.layers.Input(shape=(None,), dtype='int32', name='token_ids')
        segment_ids = keras.layers.Input(shape=(None,), dtype='int32', name='segment_ids')
        sub_head_idx = keras.layers.Input(shape=(1,), dtype='int32', name='sub_head_idx')
        sub_tail_idx = keras.layers.Input(shape=(1,), dtype='int32', name='sub_tail_idx')
        
        # BERT encoding
        bert_output = self._get_bert_layer()([token_ids, segment_ids])
        
        # Subject feature extraction
        sub_head_feature = self._seq_gather([bert_output, sub_head_idx])
        sub_tail_feature = self._seq_gather([bert_output, sub_tail_idx])
        sub_feature = keras.layers.Average()([sub_head_feature, sub_tail_feature])
        
        # Add subject information to token features
        enhanced_output = keras.layers.Add()([bert_output, sub_feature])
        
        # Object head and tail prediction
        obj_heads = keras.layers.Dense(self.num_rels, activation='sigmoid', name='obj_heads')(enhanced_output)
        obj_tails = keras.layers.Dense(self.num_rels, activation='sigmoid', name='obj_tails')(enhanced_output)
        
        model = keras.Model([token_ids, segment_ids, sub_head_idx, sub_tail_idx], [obj_heads, obj_tails])
        return model
    
    def _build_train_model(self):
        """Build training model with loss computation"""
        # All input layers
        token_ids = keras.layers.Input(shape=(None,), dtype='int32', name='token_ids')
        segment_ids = keras.layers.Input(shape=(None,), dtype='int32', name='segment_ids')
        gold_sub_heads = keras.layers.Input(shape=(None,), dtype='float32', name='gold_sub_heads')
        gold_sub_tails = keras.layers.Input(shape=(None,), dtype='float32', name='gold_sub_tails')
        sub_head_idx = keras.layers.Input(shape=(1,), dtype='int32', name='sub_head_idx')
        sub_tail_idx = keras.layers.Input(shape=(1,), dtype='int32', name='sub_tail_idx')
        gold_obj_heads = keras.layers.Input(shape=(None, self.num_rels), dtype='float32', name='gold_obj_heads')
        gold_obj_tails = keras.layers.Input(shape=(None, self.num_rels), dtype='float32', name='gold_obj_tails')
        
        # Get predictions from subject and object models
        sub_heads_pred, sub_tails_pred = self.subject_model([token_ids, segment_ids])
        obj_heads_pred, obj_tails_pred = self.object_model([token_ids, segment_ids, sub_head_idx, sub_tail_idx])
        
        model = keras.Model(
            [token_ids, segment_ids, gold_sub_heads, gold_sub_tails, 
             sub_head_idx, sub_tail_idx, gold_obj_heads, gold_obj_tails],
            [sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred]
        )
        
        return model
    
    def _get_bert_layer(self):
        """Get BERT encoding layer using torch backend"""
        def bert_layer(inputs):
            token_ids, segment_ids = inputs

            # Convert keras tensors to torch tensors for BERT processing
            import torch

            # Convert to torch tensors
            token_ids_torch = torch.tensor(keras.ops.convert_to_numpy(token_ids), dtype=torch.long)
            attention_mask = torch.ones_like(token_ids_torch)

            # Use transformers BERT model
            with torch.no_grad():
                bert_model = BertModel.from_pretrained(self.bert_model_name)
                bert_model.eval()
                outputs = bert_model(input_ids=token_ids_torch, attention_mask=attention_mask)
                sequence_output = outputs.last_hidden_state

            # Convert back to keras tensor
            return keras.ops.convert_to_tensor(sequence_output.numpy())

        return keras.layers.Lambda(bert_layer)
    
    def _seq_gather(self, inputs):
        """Gather sequence elements at specified indices"""
        seq, idxs = inputs

        def gather_fn(inputs):
            seq, idxs = inputs
            # Simplified gathering - just take the first element for now
            # This is a placeholder implementation
            return keras.ops.take_along_axis(seq, idxs, axis=1)

        return keras.layers.Lambda(gather_fn)([seq, idxs])


def load_data(train_path, dev_path, test_path, rel_dict_path):
    """Load training, dev, test data and relation mappings"""
    train_data = json.load(open(train_path))
    dev_data = json.load(open(dev_path))
    test_data = json.load(open(test_path))
    id2rel, rel2id = json.load(open(rel_dict_path))
    
    id2rel = {int(i): j for i, j in id2rel.items()}
    
    # Convert triple lists to tuples
    for dataset in [train_data, dev_data, test_data]:
        for sent in dataset:
            sent['triple_list'] = [tuple(triple) for triple in sent['triple_list']]
    
    return train_data, dev_data, test_data, id2rel, rel2id


def seq_padding(batch, padding=0):
    """Pad sequences to same length"""
    length_batch = [len(seq) for seq in batch]
    max_length = max(length_batch)
    return np.array([
        np.concatenate([seq, [padding] * (max_length - len(seq))])
        if len(seq) < max_length else seq for seq in batch
    ])


def find_head_idx(source, target):
    """Find the starting index of target in source"""
    target_len = len(target)
    for i in range(len(source)):
        if source[i: i + target_len] == target:
            return i
    return -1


class DataGenerator:
    """Data generator for training"""

    def __init__(self, data, tokenizer, rel2id, num_rels, max_length=128, batch_size=6):
        self.data = data
        self.tokenizer = tokenizer
        self.rel2id = rel2id
        self.num_rels = num_rels
        self.max_length = max_length
        self.batch_size = batch_size

    def __len__(self):
        return len(self.data) // self.batch_size + (1 if len(self.data) % self.batch_size else 0)

    def _find_entity_positions(self, tokens, entity_text):
        """Find entity positions in tokenized text"""
        entity_tokens = self.tokenizer.tokenize(entity_text)
        if not entity_tokens:
            return -1, -1

        # Remove [unused1] tokens for matching
        clean_tokens = [t for t in tokens if t != '[unused1]']
        clean_entity = [t for t in entity_tokens if t != '[unused1]']

        start_idx = find_head_idx(clean_tokens, clean_entity)
        if start_idx == -1:
            return -1, -1

        # Map back to original token positions
        original_start = 0
        clean_idx = 0
        for i, token in enumerate(tokens):
            if token != '[unused1]':
                if clean_idx == start_idx:
                    original_start = i
                    break
                clean_idx += 1

        end_idx = original_start + len(clean_entity) - 1
        return original_start, end_idx

    def __iter__(self):
        """Generate batches of training data"""
        while True:
            indices = list(range(len(self.data)))
            np.random.shuffle(indices)

            batch_data = []
            for idx in indices:
                line = self.data[idx]
                text = ' '.join(line['text'].split()[:self.max_length])

                # Tokenize
                token_ids, segment_ids = self.tokenizer.encode(text, self.max_length)
                tokens = self.tokenizer.tokenize(text)

                if len(token_ids) > self.max_length:
                    token_ids = token_ids[:self.max_length]
                    segment_ids = segment_ids[:self.max_length]
                    tokens = tokens[:self.max_length-2]  # Account for [CLS] and [SEP]

                # Initialize labels
                sub_heads = np.zeros(len(token_ids))
                sub_tails = np.zeros(len(token_ids))
                obj_heads = np.zeros((len(token_ids), self.num_rels))
                obj_tails = np.zeros((len(token_ids), self.num_rels))

                # Process triples to create labels
                for triple in line['triple_list']:
                    sub, rel, obj = triple

                    if rel not in self.rel2id:
                        continue

                    rel_id = self.rel2id[rel]

                    # Find subject positions
                    sub_start, sub_end = self._find_entity_positions(tokens, sub)
                    if sub_start != -1:
                        # Adjust for [CLS] token
                        sub_start += 1
                        sub_end += 1
                        if sub_start < len(token_ids) and sub_end < len(token_ids):
                            sub_heads[sub_start] = 1
                            sub_tails[sub_end] = 1

                    # Find object positions
                    obj_start, obj_end = self._find_entity_positions(tokens, obj)
                    if obj_start != -1:
                        # Adjust for [CLS] token
                        obj_start += 1
                        obj_end += 1
                        if obj_start < len(token_ids) and obj_end < len(token_ids):
                            obj_heads[obj_start, rel_id] = 1
                            obj_tails[obj_end, rel_id] = 1

                # Use first subject as example for object model
                sub_head_idx = np.where(sub_heads == 1)[0]
                sub_tail_idx = np.where(sub_tails == 1)[0]

                if len(sub_head_idx) > 0 and len(sub_tail_idx) > 0:
                    batch_data.append({
                        'token_ids': token_ids,
                        'segment_ids': segment_ids,
                        'sub_heads': sub_heads,
                        'sub_tails': sub_tails,
                        'obj_heads': obj_heads,
                        'obj_tails': obj_tails,
                        'sub_head_idx': [sub_head_idx[0]],
                        'sub_tail_idx': [sub_tail_idx[0]],
                    })

                    if len(batch_data) == self.batch_size:
                        yield self._prepare_batch(batch_data)
                        batch_data = []

            if batch_data:
                yield self._prepare_batch(batch_data)
    
    def _prepare_batch(self, batch_data):
        """Prepare batch for training"""
        batch = {}
        for key in batch_data[0].keys():
            if key in ['token_ids', 'segment_ids', 'sub_heads', 'sub_tails']:
                batch[key] = seq_padding([item[key] for item in batch_data])
            elif key in ['obj_heads', 'obj_tails']:
                batch[key] = seq_padding([item[key] for item in batch_data], 
                                       np.zeros(self.num_rels))
            else:
                batch[key] = np.array([item[key] for item in batch_data])
        
        inputs = [batch['token_ids'], batch['segment_ids'], batch['sub_heads'], 
                 batch['sub_tails'], batch['sub_head_idx'], batch['sub_tail_idx'],
                 batch['obj_heads'], batch['obj_tails']]
        
        return inputs, None  # No targets needed as loss is computed in model


def extract_triples(subject_model, object_model, tokenizer, text, id2rel, h_bar=0.5, t_bar=0.5):
    """Extract triples from text using trained models"""
    tokens = tokenizer.tokenize(text)
    token_ids, segment_ids = tokenizer.encode(text)

    if len(token_ids) > 512:
        token_ids = token_ids[:512]
        segment_ids = segment_ids[:512]
        tokens = tokens[:510]  # Account for [CLS] and [SEP]

    # Convert to numpy arrays and add batch dimension
    token_ids = np.array([token_ids])
    segment_ids = np.array([segment_ids])

    # Get subject predictions
    sub_heads_logits, sub_tails_logits = subject_model.predict([token_ids, segment_ids])
    sub_heads = np.where(sub_heads_logits[0] > h_bar)[0]
    sub_tails = np.where(sub_tails_logits[0] > t_bar)[0]

    # Find valid subjects
    subjects = []
    for sub_head in sub_heads:
        sub_tail = sub_tails[sub_tails >= sub_head]
        if len(sub_tail) > 0:
            sub_tail = sub_tail[0]
            if sub_head > 0 and sub_tail < len(tokens) + 1:  # Account for [CLS]
                subject_tokens = tokens[sub_head-1:sub_tail]  # Adjust for [CLS]
                subject_text = ''.join([t.lstrip("##") for t in subject_tokens])
                subject_text = ' '.join(subject_text.split('[unused1]'))
                subjects.append((subject_text, sub_head, sub_tail))

    if not subjects:
        return []

    # Get object predictions for each subject
    triple_list = []
    for subject_text, sub_head, sub_tail in subjects:
        # Prepare inputs for object model
        sub_head_idx = np.array([[sub_head]])
        sub_tail_idx = np.array([[sub_tail]])

        obj_heads_logits, obj_tails_logits = object_model.predict([
            token_ids, segment_ids, sub_head_idx, sub_tail_idx
        ])

        obj_heads = np.where(obj_heads_logits[0] > h_bar)
        obj_tails = np.where(obj_tails_logits[0] > t_bar)

        for obj_head, rel_head in zip(*obj_heads):
            for obj_tail, rel_tail in zip(*obj_tails):
                if obj_head <= obj_tail and rel_head == rel_tail:
                    if obj_head > 0 and obj_tail < len(tokens) + 1:  # Account for [CLS]
                        rel = id2rel[rel_head]
                        object_tokens = tokens[obj_head-1:obj_tail]  # Adjust for [CLS]
                        object_text = ''.join([t.lstrip("##") for t in object_tokens])
                        object_text = ' '.join(object_text.split('[unused1]'))
                        triple_list.append((subject_text, rel, object_text))
                        break

    # Remove duplicates
    return list(set(triple_list))


def partial_match(pred_set, gold_set):
    """Apply partial match evaluation (compare only first word of entities)"""
    pred = {(
        i[0].split(' ')[0] if len(i[0].split(' ')) > 0 else i[0],
        i[1],
        i[2].split(' ')[0] if len(i[2].split(' ')) > 0 else i[2]
    ) for i in pred_set}

    gold = {(
        i[0].split(' ')[0] if len(i[0].split(' ')) > 0 else i[0],
        i[1],
        i[2].split(' ')[0] if len(i[2].split(' ')) > 0 else i[2]
    ) for i in gold_set}

    return pred, gold


def evaluate_model(subject_model, object_model, eval_data, tokenizer, id2rel, exact_match=False):
    """Evaluate model performance"""
    correct_num, predict_num, gold_num = 1e-10, 1e-10, 1e-10

    for line in tqdm(eval_data, desc="Evaluating"):
        pred_triples = set(extract_triples(subject_model, object_model, tokenizer, line['text'], id2rel))
        gold_triples = set(line['triple_list'])

        if exact_match:
            pred_eval, gold_eval = pred_triples, gold_triples
        else:
            pred_eval, gold_eval = partial_match(pred_triples, gold_triples)

        correct_num += len(pred_eval & gold_eval)
        predict_num += len(pred_eval)
        gold_num += len(gold_eval)

    precision = correct_num / predict_num
    recall = correct_num / gold_num
    f1_score = 2 * precision * recall / (precision + recall)

    return precision, recall, f1_score


def train_model(model, train_generator, dev_data, tokenizer, id2rel, epochs=100, lr=1e-5):
    """Train the CasRel model"""
    # Compile model with custom loss
    optimizer = keras.optimizers.Adam(learning_rate=lr)

    # Custom loss function that mimics the original Keras implementation
    def casrel_loss(y_true, y_pred):
        # y_pred contains [sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred]
        # y_true contains the same structure from inputs

        # For now, use a simple MSE loss as placeholder
        # In a full implementation, you'd compute the exact loss from the original paper
        total_loss = 0
        for i in range(len(y_pred)):
            total_loss += keras.ops.mean(keras.ops.square(y_pred[i] - y_true[i]))

        return total_loss / len(y_pred)

    # Compile with multiple outputs
    model.train_model.compile(
        optimizer=optimizer,
        loss=['binary_crossentropy', 'binary_crossentropy', 'binary_crossentropy', 'binary_crossentropy'],
        loss_weights=[1.0, 1.0, 1.0, 1.0]
    )

    best_f1 = 0
    steps_per_epoch = len(train_generator)

    for epoch in range(epochs):
        print(f'Epoch {epoch + 1}/{epochs}')

        # Training step
        epoch_loss = 0
        num_batches = 0

        for step, (batch_inputs, _) in enumerate(train_generator):
            if step >= steps_per_epoch:
                break

            # Prepare targets (same as inputs for this implementation)
            targets = [
                batch_inputs[2],  # sub_heads
                batch_inputs[3],  # sub_tails
                batch_inputs[6],  # obj_heads
                batch_inputs[7],  # obj_tails
            ]

            # Train on batch
            loss = model.train_model.train_on_batch(batch_inputs, targets)
            epoch_loss += loss if isinstance(loss, (int, float)) else sum(loss)
            num_batches += 1

            if step % 50 == 0:
                avg_loss = epoch_loss / max(num_batches, 1)
                print(f'Step {step}/{steps_per_epoch}, Avg Loss: {avg_loss:.4f}')

        avg_epoch_loss = epoch_loss / max(num_batches, 1)
        print(f'Epoch {epoch + 1} completed, Avg Loss: {avg_epoch_loss:.4f}')

        # Evaluation
        if epoch % 5 == 0 or epoch == epochs - 1:  # Evaluate every 5 epochs and at the end
            print('Evaluating on dev set...')
            precision, recall, f1 = evaluate_model(
                model.subject_model, model.object_model, dev_data, tokenizer, id2rel
            )
            print(f'Epoch {epoch + 1}: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}')

            if f1 > best_f1:
                best_f1 = f1
                # Save best model
                model.subject_model.save_weights('best_subject_model.weights.h5')
                model.object_model.save_weights('best_object_model.weights.h5')
                print(f'New best F1: {best_f1:.3f}, model saved')

    print(f'Training completed. Best F1: {best_f1:.3f}')


def main():
    parser = argparse.ArgumentParser(description='CasRel Keras-Core Implementation')
    parser.add_argument('--train', action='store_true', help='Train the model')
    parser.add_argument('--dataset', default='WebNLG', type=str, 
                       help='Dataset name')
    parser.add_argument('--batch_size', default=6, type=int, help='Batch size')
    parser.add_argument('--epochs', default=100, type=int, help='Number of epochs')
    parser.add_argument('--lr', default=1e-5, type=float, help='Learning rate')
    parser.add_argument('--max_length', default=128, type=int, help='Max sequence length')
    
    args = parser.parse_args()
    
    # Data paths
    dataset = args.dataset
    train_path = f'data/{dataset}/train_triples.json'
    dev_path = f'data/{dataset}/dev_triples.json'
    test_path = f'data/{dataset}/test_triples.json'
    rel_dict_path = f'data/{dataset}/rel2id.json'
    
    # Load data
    train_data, dev_data, test_data, id2rel, rel2id = load_data(
        train_path, dev_path, test_path, rel_dict_path
    )
    
    print(f'Loaded {len(train_data)} train, {len(dev_data)} dev, {len(test_data)} test samples')
    print(f'Number of relations: {len(rel2id)}')
    
    # Initialize model
    model = CasRelModel('bert-base-cased', len(rel2id), args.max_length)

    if args.train:
        print('Starting training...')

        # Create data generator
        train_generator = DataGenerator(
            train_data, model.tokenizer, rel2id, len(rel2id),
            args.max_length, args.batch_size
        )

        # Train model
        train_model(model, train_generator, dev_data, model.tokenizer, id2rel, args.epochs, args.lr)

    else:
        # Load trained model weights
        try:
            model.subject_model.load_weights('best_subject_model.weights.h5')
            model.object_model.load_weights('best_object_model.weights.h5')
            print('Loaded trained model weights')
        except:
            print('No trained model found. Please train first with --train')
            return

    # Final evaluation on test set
    print('Evaluating on test set...')
    precision, recall, f1 = evaluate_model(
        model.subject_model, model.object_model, test_data, model.tokenizer, id2rel
    )
    print(f'Final Test Results: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}')

    # Example usage
    if not args.train:
        print('\nExample extraction:')
        example_text = "Barack Obama was born in Hawaii."
        triples = extract_triples(
            model.subject_model, model.object_model, model.tokenizer,
            example_text, id2rel
        )
        print(f'Text: {example_text}')
        print(f'Extracted triples: {triples}')


if __name__ == '__main__':
    main()


# Usage Example:
"""
To use this implementation:

1. Install dependencies:
   pip install keras-core torch transformers tqdm numpy

2. Prepare your data in the same format as the original CasRel:
   - train_triples.json
   - dev_triples.json
   - test_triples.json
   - rel2id.json

3. Train the model:
   python casrel_keras.py --train --dataset WebNLG --epochs 50 --batch_size 6

4. Evaluate the model:
   python casrel_keras.py --dataset WebNLG

Key Features:
- Uses keras-core with torch backend
- Implements the same two-stage extraction as original CasRel
- Supports partial match evaluation (comparing only first word of entities)
- Compatible with the original data format
- Modular design with separate subject and object models

Note: This implementation uses a simplified BERT integration. For production use,
you may want to implement a more sophisticated BERT layer conversion or use
a different approach for BERT integration with keras-core.
"""
